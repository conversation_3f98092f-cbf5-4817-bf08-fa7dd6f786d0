// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Core.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.ProductionPlans.Dto;

/// <summary>
/// 生产计划输出参数
/// </summary>
public class ProductionPlanOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 计划编号
    /// </summary>
    public string PlanCode { get; set; }
    /// <summary>
    /// 产品规格型号
    /// </summary>
    public string Specification { get; set; }
    /// <summary>
    /// 产品计量单位（个/件/箱等）
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string PlanName { get; set; }

    /// <summary>
    /// 工单数量
    /// </summary>
    public int? WorkOrderNumber { get; set; }

    /// <summary>
    /// 来源类型Id
    /// </summary>
    public long? SourceId { get; set; }

    /// <summary>
    /// 来源类型名称
    /// </summary>
    public string SourceName { get; set; }

    /// <summary>
    /// 成品Id
    /// </summary>
    public long? ProductId { get; set; }

    /// <summary>
    /// 成品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 成品编号
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 成品规格
    /// </summary>
    public string ProductSpecification { get; set; }

    /// <summary>
    /// 成品单位
    /// </summary>
    public string ProductUnit { get; set; }

    /// <summary>
    /// 计划数量
    /// </summary>
    public int? PlanNumber { get; set; }

    /// <summary>
    /// 计划开工时间
    /// </summary>
    public DateTime? PlanStartTime { get; set; }

    /// <summary>
    /// 计划完工时间
    /// </summary>
    public DateTime? PlanEndTime { get; set; }

    /// <summary>
    /// 需求时间
    /// </summary>
    public DateTime? DemandTime { get; set; }

    /// <summary>
    /// 计划状态
    /// </summary>
    public int? PlanStatus { get; set; }

    /// <summary>
    /// 计划状态名称
    /// </summary>
    public string PlanStatusName => PlanStatus switch
    {
        0 => "未分解",
        1 => "已分解",
        2 => "已完成",
        3 => "已关闭",
        4 => "已撤回",
        5 => "进行中",
        _ => "未知状态"
    };

    /// <summary>
    /// 计划备注
    /// </summary>
    public string PlanRemark { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    public string PlanAttachment { get; set; }

    /// <summary>
    /// BOM Id
    /// </summary>
    public long? BomId { get; set; }

    /// <summary>
    /// 订单Id (外键)
    /// </summary>
    public long? OrderId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}

/// <summary>
/// 批量删除生产计划结果
/// </summary>
public class BatchDeleteProductionPlanOutput
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 成功删除的数量
    /// </summary>
    public int DeletedCount { get; set; }

    /// <summary>
    /// 失败删除的数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 删除失败的详细信息
    /// </summary>
    public List<DeleteFailureInfo> Failures { get; set; } = new List<DeleteFailureInfo>();
}

/// <summary>
/// 删除失败信息
/// </summary>
public class DeleteFailureInfo
{
    /// <summary>
    /// 生产计划ID
    /// </summary>
    public long PlanId { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string Reason { get; set; }
} 